import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

from src.core.security import get_current_user_ws
from src.pipeline.state import ConnectionState, CallState, connection_state_manager
from src.pipeline.tasks import run_pipeline
from src.core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()


@router.websocket("/ws/call/{call_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    call_id: str,
):
    """
    WebSocket endpoint for real-time voice translation.

    This endpoint handles the complete lifecycle of a voice translation call:
    1. WebSocket connection establishment
    2. Authentication via JWT token in query parameters
    3. Real-time audio processing pipeline
    4. Call termination and cleanup

    Args:
        websocket: WebSocket connection
        call_id: Unique identifier for the call
    """
    # Accept the WebSocket connection first to complete ASGI handshake
    await websocket.accept()

    try:
        # Extract token from query parameters manually
        token = websocket.query_params.get("token")
        if not token:
            await websocket.close(code=4001, reason="Access token is required")
            return

        try:
            # Authenticate user after accepting connection
            user = await get_current_user_ws(token=token)
            pass
        except Exception as e:
            logger.warning(f"Authentication failed for call {call_id}: {str(e)}")
            logger.exception(e)
            await websocket.close(code=4001, reason="Invalid or expired access token")
            return

        logger.info(f"WebSocket connection established for call {call_id} by user {user.user_id}")

    except Exception as e:
        logger.error(f"Error during WebSocket authentication for call {call_id}: {str(e)}")
        await websocket.close(code=4000, reason="Authentication error")
        return
    
    # Create connection state
    state = ConnectionState(
        websocket=websocket,
        user=user,
        call_id=call_id,
        state=CallState.ACTIVE,
    )
    
    # Add to connection manager
    await connection_state_manager.add_connection(state)
    
    try:
        # Send initial connection confirmation
        await websocket.send_json({
            "type": "connection_established",
            "call_id": call_id,
            "message": "Voice translation session started",
            "timestamp": state.connected_at,
        })
        
        # Start the main processing pipeline
        await run_pipeline(state)
        
    except WebSocketDisconnect:
        logger.info(f"Client for call {call_id} disconnected")
        state.state = CallState.ENDING
        
    except Exception as e:
        logger.error(f"Error in WebSocket connection {call_id}: {str(e)}")
        state.add_error("websocket_error", str(e))
        state.state = CallState.ERROR
        
        # Try to send error message to client if connection is still open
        if websocket.client_state == WebSocketState.CONNECTED:
            try:
                await websocket.send_json({
                    "type": "error",
                    "message": "An error occurred during the call",
                    "call_id": call_id,
                })
            except Exception:
                pass  # Connection might be closed
                
    finally:
        # Handle call termination
        await handle_call_termination(state)


async def handle_call_termination(state: ConnectionState) -> None:
    """
    Handle call termination and cleanup.
    
    This function performs the following actions:
    1. Persist call data to CallDataService
    2. Publish CallEnded event to Kafka
    3. Enqueue background analytics task
    4. Clean up connection resources
    
    Args:
        state: Connection state to terminate
    """
    logger.info(f"Terminating call {state.call_id}")
    
    try:
        # Update state
        state.state = CallState.ENDING
        
        # 1. Persist call data to CallDataService
        await persist_call_data(state)
        
        # 2. Publish CallEnded event to Kafka
        await publish_call_ended_event(state)
        
        # Send final message to client if connection is still open
        if state.websocket.client_state == WebSocketState.CONNECTED:
            try:
                await state.websocket.send_json({
                    "type": "call_ended",
                    "call_id": state.call_id,
                    "message": "Call ended successfully",
                    "stats": state.get_stats(),
                })
            except Exception:
                pass  # Connection might be closed
        
    except Exception as e:
        logger.error(f"Error during call termination for {state.call_id}: {str(e)}")
        state.add_error("termination_error", str(e))
    
    finally:
        # 4. Clean up connection resources
        await state.cleanup()
        await connection_state_manager.remove_connection(state.call_id)
        state.state = CallState.ENDED
        
        logger.info(f"Call {state.call_id} terminated and cleaned up")


async def persist_call_data(state: ConnectionState) -> None:
    """
    Persist call data to CallDataService.
    
    Args:
        state: Connection state with call data
    """
    try:
        import httpx
        
        # Prepare call data
        call_data = {
            "call_id": state.call_id,
            "user_id": str(state.user.user_id),
            "started_at": state.connected_at,
            "ended_at": state.last_activity,
            "duration": state.get_connection_duration(),
            "transcript": [
                {
                    "timestamp": entry.timestamp,
                    "original_text": entry.original_text,
                    "translated_text": entry.translated_text,
                    "speaker": entry.speaker,
                    "confidence": entry.confidence,
                }
                for entry in state.transcript
            ],
            "stats": state.get_stats(),
            "errors": state.errors,
        }
        
        # Make HTTP request to CallDataService
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{settings.call_data_service_url}/api/v1/calls",
                json=call_data,
                timeout=10.0,
            )
            response.raise_for_status()
            
        logger.info(f"Call data persisted for call {state.call_id}")
        
    except Exception as e:
        logger.error(f"Failed to persist call data for {state.call_id}: {str(e)}")
        state.add_error("persistence_error", str(e))


async def publish_call_ended_event(state: ConnectionState) -> None:
    """
    Publish CallEnded event to Kafka.
    
    Args:
        state: Connection state with call data
    """
    try:
        from cortexacommon.events.producer import get_kafka_producer
        
        # Create event data
        event_data = {
            "call_id": state.call_id,
            "user_id": str(state.user.user_id),
            "ended_at": state.last_activity,
            "duration": state.get_connection_duration(),
            "transcript_entries": len(state.transcript),
            "errors": len(state.errors),
        }
        
        # Publish event
        producer = get_kafka_producer()
        await producer.send("call_ended", value=event_data)
        
        logger.info(f"CallEnded event published for call {state.call_id}")
        
    except Exception as e:
        logger.error(f"Failed to publish CallEnded event for {state.call_id}: {str(e)}")
        state.add_error("event_publish_error", str(e))


@router.get("/health")
async def health_check():
    """Health check endpoint."""
    connection_count = await connection_state_manager.get_connection_count()
    return {
        "status": "healthy",
        "service": "voice-gateway",
        "active_connections": connection_count,
        "max_connections": settings.ws_max_connections,
    }


@router.get("/stats")
async def get_stats():
    """Get service statistics."""
    connections = await connection_state_manager.get_all_connections()
    
    stats = {
        "active_connections": len(connections),
        "max_connections": settings.ws_max_connections,
        "connections": [conn.get_stats() for conn in connections],
    }
    
    return stats
