import asyncio
import logging
import time
from typing import Optional, <PERSON>ple
import numpy as np
import wave  # Added for WAV file operations
import os    # Added for directory creation

from src.pipeline.state import ConnectionState
from src.core.config import settings

logger = logging.getLogger(__name__)


class VADProcessor:
    """Voice Activity Detection processor using WebRTC VAD."""
    
    def __init__(self, aggressiveness: int = 3, frame_duration_ms: int = 30):
        self.aggressiveness = aggressiveness
        self.frame_duration_ms = frame_duration_ms
        self.frame_size = int(settings.audio_sample_rate * frame_duration_ms / 1000)
        self._vad = None
        self._initialize_vad()
    
    def _initialize_vad(self):
        """Initialize WebRTC VAD."""
        try:
            import webrtcvad
            self._vad = webrtcvad.Vad(self.aggressiveness)
            logger.info(f"VAD initialized with aggressiveness {self.aggressiveness}")
        except ImportError:
            logger.error("webrtcvad not available, VAD disabled")
            self._vad = None
    
    def is_speech(self, audio_frame: bytes) -> bool:
        """
        Detect if audio frame contains speech.
        
        Args:
            audio_frame: Audio frame bytes (16kHz, 16-bit, mono)
            
        Returns:
            bool: True if speech detected
        """
        if self._vad is None:
            return True  # Assume speech if VAD not available
        
        try:
            # Ensure frame is correct size
            if len(audio_frame) != self.frame_size * 2:  # 2 bytes per sample
                return False
            
            return self._vad.is_speech(audio_frame, settings.audio_sample_rate)
        except Exception as e:
            logger.warning(f"VAD error: {e}")
            return True  # Assume speech on error


class STTProcessor:
    """Speech-to-Text processor using Faster Whisper."""
    
    def __init__(self):
        self._model = None
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize Faster Whisper model."""
        try:
            from faster_whisper import WhisperModel
            
            self._model = WhisperModel(
                settings.whisper_model_size,
                device=settings.whisper_device,
                compute_type=settings.whisper_compute_type,
            )
            logger.info(f"Whisper model {settings.whisper_model_size} initialized")
        except ImportError:
            logger.error("faster-whisper not available")
            self._model = None
        except Exception as e:
            logger.error(f"Failed to initialize Whisper model: {e}")
            self._model = None
    
    async def transcribe(self, audio_data: bytes) -> Tuple[str, float]:
        """
        Transcribe audio to text.
        
        Args:
            audio_data: Audio data bytes
            
        Returns:
            Tuple of (transcribed_text, confidence_score)
        """
        if self._model is None:
            return "Transcription not available", 0.0
        
        try:
            # Convert bytes to numpy array
            audio_np = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            
            # Run transcription in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            segments, info = await loop.run_in_executor(
                None, 
                lambda: self._model.transcribe(audio_np, language="en")
            )
            
            # Combine segments into single text
            text_parts = []
            total_confidence = 0.0
            segment_count = 0
            
            for segment in segments:
                text_parts.append(segment.text.strip())
                total_confidence += getattr(segment, 'avg_logprob', 0.0)
                segment_count += 1
            
            text = " ".join(text_parts).strip()
            confidence = total_confidence / segment_count if segment_count > 0 else 0.0
            
            return text, confidence
            
        except Exception as e:
            logger.error(f"STT error: {e}")
            return "", 0.0


class TranslationProcessor:
    """Translation processor using Hugging Face transformers."""
    
    def __init__(self):
        self._pipeline = None
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize translation model."""
        try:
            from transformers import pipeline
            
            self._pipeline = pipeline(
                "translation",
                model=settings.translation_model,
                device=0 if settings.translation_device == "cuda" else -1,
            )
            logger.info(f"Translation model {settings.translation_model} initialized")
        except ImportError:
            logger.error("transformers not available")
            self._pipeline = None
        except Exception as e:
            logger.error(f"Failed to initialize translation model: {e}")
            self._pipeline = None
    
    async def translate(self, text: str) -> str:
        """
        Translate text to target language.
        
        Args:
            text: Text to translate
            
        Returns:
            Translated text
        """
        if not text.strip() or self._pipeline is None:
            return text
        
        try:
            # Run translation in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: self._pipeline(text, max_length=512)
            )
            
            if result and len(result) > 0:
                return result[0]['translation_text']
            
            return text
            
        except Exception as e:
            logger.error(f"Translation error: {e}")
            return text


class TTSProcessor:
    """Text-to-Speech processor using OpenAI TTS API."""
    
    def __init__(self):
        self._client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize TTS client."""
        if settings.tts_provider == "openai" and settings.tts_api_key:
            try:
                import openai
                self._client = openai.AsyncOpenAI(api_key=settings.tts_api_key)
                logger.info("OpenAI TTS client initialized")
            except ImportError:
                logger.error("openai package not available")
                self._client = None
        else:
            logger.warning("TTS not configured")
            self._client = None
    
    async def synthesize(self, text: str) -> Optional[bytes]:
        """
        Synthesize speech from text.
        
        Args:
            text: Text to synthesize
            
        Returns:
            Audio bytes or None if synthesis fails
        """
        if not text.strip() or self._client is None:
            return None
        
        try:
            response = await self._client.audio.speech.create(
                model=settings.tts_model,
                voice=settings.tts_voice,
                input=text,
                response_format="wav",
            )
            
            return response.content
            
        except Exception as e:
            logger.error(f"TTS error: {e}")
            return None


class S2STProcessor:
    """Main Speech-to-Speech Translation processor."""
    
    def __init__(self):
        self.vad = VADProcessor(
            aggressiveness=settings.vad_aggressiveness,
            frame_duration_ms=settings.vad_frame_duration_ms,
        )
        self.stt = STTProcessor()
        self.translator = TranslationProcessor()
        self.tts = TTSProcessor()

        # Add a buffer for incoming audio before VAD
        self.audio_buffer = bytearray()
        self.vad_frame_size_bytes = self.vad.frame_size * 2 # Calculate once: 480 * 2 = 960
        
        # Processing parameters
        self.min_speech_duration = 1.0  # Minimum speech duration in seconds
        self.max_silence_duration = 2.0  # Maximum silence before processing
        self.speech_buffer = bytearray()
        self.last_speech_time = 0.0
        self.is_in_speech = False

    async def process_audio_chunk(self, state: ConnectionState) -> None:
        """
        Process audio chunks from the connection state.
        ...
        """
        logger.info(f"Starting S2ST processor for call {state.call_id}")
        
        try:
            while state.state.value in ["active", "processing"]:
                try:
                    # Get audio chunk from inbound queue
                    raw_audio_chunk = await asyncio.wait_for(
                        state.inbound_queue.get(), 
                        timeout=1.0
                    )

                    # Add incoming data to our buffer
                    self.audio_buffer.extend(raw_audio_chunk)

                    # Process all full frames available in the buffer
                    while len(self.audio_buffer) >= self.vad_frame_size_bytes:
                        # Extract one frame for VAD processing
                        vad_frame = self.audio_buffer[:self.vad_frame_size_bytes]
                        
                        # Remove the processed frame from the buffer
                        self.audio_buffer = self.audio_buffer[self.vad_frame_size_bytes:]

                        # Process the correctly-sized frame
                        await self._process_chunk(state, vad_frame)
                    
                    # Mark task as done
                    state.inbound_queue.task_done()
                    
                except asyncio.TimeoutError:
                    # Check for pending speech to process
                    await self._check_pending_speech(state)
                    continue
                    
                except Exception as e:
                    logger.error(f"Error processing audio chunk: {e}")
                    state.add_error("processing_error", str(e))
                    
        except Exception as e:
            logger.error(f"S2ST processor error for call {state.call_id}: {e}")
            state.add_error("s2st_processor_error", str(e))
        
        logger.info(f"S2ST processor ended for call {state.call_id}")

    async def _process_chunk(self, state: ConnectionState, audio_chunk: bytes) -> None:
        """Process a single, correctly-sized VAD audio frame."""
        current_time = time.time()
        
        # This check will now pass, because `audio_chunk` is exactly vad_frame_size_bytes
        is_speech = self.vad.is_speech(audio_chunk)
        
        if is_speech:
            if not self.is_in_speech:
                # Start of speech segment
                self.is_in_speech = True
                self.speech_buffer.clear()
                logger.debug(f"Speech started for call {state.call_id}")
            
            self.speech_buffer.extend(audio_chunk)
            self.last_speech_time = current_time
            state.last_vad_activity = current_time
            
        else:
            if self.is_in_speech:
                # Check if we should end the speech segment
                silence_duration = current_time - self.last_speech_time
                
                if silence_duration >= self.max_silence_duration:
                    # End of speech segment - process it
                    await self._process_speech_segment(state)
                    self.is_in_speech = False
    
    async def _check_pending_speech(self, state: ConnectionState) -> None:
        """Check if there's pending speech to process."""
        if self.is_in_speech and len(self.speech_buffer) > 0:
            current_time = time.time()
            silence_duration = current_time - self.last_speech_time
            
            if silence_duration >= self.max_silence_duration:
                await self._process_speech_segment(state)
                self.is_in_speech = False
    
    def _save_audio_for_debug(self, call_id: str, audio_data: bytes):
        """Saves the captured audio segment to a WAV file for debugging."""
        try:
            # Create a directory for debug audio if it doesn't exist
            debug_dir = "debug_audio"
            os.makedirs(debug_dir, exist_ok=True)
            
            # Generate a unique filename using call_id and timestamp
            timestamp = int(time.time() * 1000)
            filename = os.path.join(debug_dir, f"segment_{call_id}_{timestamp}.wav")
            
            # Write the WAV file
            # The audio is 16-bit PCM, 1-channel (mono)
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)  # 2 bytes for 16-bit audio
                wf.setframerate(settings.audio_sample_rate)
                wf.writeframes(audio_data)
                
            logger.info(f"Saved debug audio to {filename}")
            
        except Exception as e:
            logger.error(f"Failed to save debug audio: {e}")

    async def _process_speech_segment(self, state: ConnectionState) -> None:
        """Process a complete speech segment through the S2ST pipeline."""
        if len(self.speech_buffer) == 0:
            return
        
        # Check minimum duration
        duration = len(self.speech_buffer) / (settings.audio_sample_rate * 2)  # 2 bytes per sample
        if duration < self.min_speech_duration:
            logger.debug(f"Speech segment too short ({duration:.2f}s), skipping")
            self.speech_buffer.clear()
            return
        
        logger.info(f"Processing speech segment ({duration:.2f}s) for call {state.call_id}")
        state.is_processing = True
        
        try:
            # 1. Speech-to-Text
            speech_data = bytes(self.speech_buffer)
            
            # --- ADDED FOR DEBUGGING ---
            # Save the captured audio segment to a file before transcription
            self._save_audio_for_debug(state.call_id, speech_data)
            # ---------------------------

            text, confidence = await self.stt.transcribe(speech_data)
            
            if not text.strip():
                logger.debug("No text transcribed, skipping segment")
                return
            
            logger.info(f"Transcribed: '{text}' (confidence: {confidence:.2f})")
            
            # 2. Translation
            translated_text = await self.translator.translate(text)
            logger.info(f"Translated: '{translated_text}'")
            
            # 3. Text-to-Speech
            tts_audio = await self.tts.synthesize(translated_text)
            
            if tts_audio:
                # Send translated audio to outbound queue
                await state.outbound_queue.put(tts_audio)
                state.total_audio_sent += len(tts_audio)
                logger.info(f"TTS audio sent ({len(tts_audio)} bytes)")
            
            # 4. Add to transcript
            state.add_transcript_entry(text, translated_text, confidence)
            state.segments_processed += 1
            
        except Exception as e:
            logger.error(f"Error processing speech segment: {e}")
            state.add_error("segment_processing_error", str(e))
        
        finally:
            self.speech_buffer.clear()
            state.is_processing = False